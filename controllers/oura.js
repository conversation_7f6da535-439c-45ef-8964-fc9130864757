const { v4: uuidv4 } = require("uuid");

const { log } = require("../utils/logger");
const { getDateAfterSeconds, getDuration } = require("../utils/helpers");
const { config } = require("../environment/index");

const oura = require("../sources/oura");
const { userService } = require("../service/users")
const { devicesService } = require("../service/devices");
const { trackersService } = require("../service/trackers");

const { getStaticSources, getStaticSourceDevices } = require("../utils/staticData");
const { getUTCOffsetValue } = require("../service/userProfile");
const { addLogsToQueue } = require("../utils/trackersIngestion");

const trackersOuraScopeMap = {
  email: [], personal: [], daily: [3, 5], 
  heartrate: [10, 11, 25], workout: [], tag: [], session: [], spo2: [9]
}

// Auth Redirect
async function getMetadata(req, res, next) {
  const userId = req.decoded.user_guid;
  if (!userId) {
    return res.status(400).send({ success: false, message: "userId is missing in request" });
  }
  
  const authorizeUrl = await getOAuthUrl(userId);
  return res.status(200).json({
    success: true,
    message: 'Visit Authorise URL to proceed with OAuth Login.',
    data: {
      authorizeUrl,
    },
  });
};

// Auth Callback
async function callback(req, res, next) {
  try {
    const code = req.query.code;
    const state = req.query.state;
    const scope = req.query.scope;
    if (!code || !state) {
      return res.status(400).send({
        success: false,
        message: "Required query param code/state missing",
      });
    }
    const sources = await getStaticSources();
    const userDetails = await userService.getUserByState(state, sources.Oura.id);
    log.debug(`Oura callback userDetails`, JSON.stringify(userDetails));
    if (!userDetails) {
      return res.status(400).send({
        success: false,
        message: "Invalid state input provided",
      });
    }

    const ouraResponse = await oura.auth.getAccessToken(code);
    log.debug(`getAccessToken for userId: ${userDetails.userId}`, JSON.stringify(ouraResponse));

    await storeOuraDetails(userDetails.userId, ouraResponse);
    const accessToken = req.headers['x-access-token'];
    await updateDeviceScope(userDetails.userId, scope, accessToken);

    // Syncing logs for duration = 7, once device is connected
    const ouraLogs = await syncOuraData(userDetails, 7);

    return res.status(200).json({
      success: true,
      message: "Success",
    });
  } catch (error) {
    log.error(`Oura callback error: ${error.message}`, error);
    return next({ message: error.message, statusCode: 500 });
  }
};

// Revoke Token
async function revokeOuraToken(userId, isRefreshTokenValid = true) {
  log.info(`Revoking oura token for userId: ${userId}`);
  const sources = await getStaticSources();
  const sourceDevices = await getStaticSourceDevices();
  if(isRefreshTokenValid) {
    const token = await getOuraAccessToken(userId)

    // Revoke access token
    const deletedToken = await oura.auth.revokeToken(token)
    if (!deletedToken) { log.info("Invalid request, can't revoke token") }
  }

  // Remove user Oura details
  const deletedUserDoc = await userService.deleteUser(userId, sources.Oura.id);
  if (!deletedUserDoc) {log.info("Failed to delete user")}
  
  // Remove Oura default device
  const resp = await trackersService.getAllDefaultTrackers(userId)
  if(resp && resp.defaultDevices) {
    resp.defaultDevices = resp.defaultDevices.filter(d => {
      d.deviceId != sourceDevices[sources.Oura.id]?.[0]?.id
    })
    await trackersService.upsertTrackers(userId, resp)
  }
  // Remove connected device
  const deviceIds = Object.keys(deletedUserDoc?.devices || {});
  await devicesService.deleteConnectedDevice(userId, deviceIds);

  log.info(`Revoked user token for Oura for userId: ${userId}`)
}

async function storeOuraDetails(userId, ouraResponse) {
  const sources = await getStaticSources();
  const data = {
    accessToken: ouraResponse?.access_token,
    refreshToken: ouraResponse?.refresh_token,
  };
  data.expiresAt =
    data.accessToken && ouraResponse.expires_in
      ? getDateAfterSeconds(ouraResponse.expires_in - 60)
      : null;
  await userService.upsertUserDetails(userId, data, sources.Oura.id);
}

async function ManualSync(req, res, next) {
  try {
    const userId = req.query?.user_guid || req.decoded.user_guid;
    let duration = Number(req.query?.duration || -1);
    if (!userId)
      return res.status(400).send({ success: false, message: "user_id missing in request", });

    const sources = await getStaticSources();
    const userDetails = await userService.getUserDetails(userId, sources.Oura.id);
    if (!userDetails)
      return res.status(400).send({ success: false, message: "User is not connected with Oura", });

    /**
     * Conditions to modify duration, incase not passed in query param
     * if lastSynced exists ->
     *    if timeDiff between current time & lastSynced is greater than 7 days, then duration = 7
     *    else duration = 0
     * if lastSynced doesn't exist ->
     *    then duration = 7
    */
    if (duration === -1) {
      if (!userDetails.lastSynced) {
        duration = 7;
      } else {
        const lastSyncedDate = new Date(userDetails.lastSynced);
        const timeDiff = getDuration(lastSyncedDate.toISOString(), new Date().toISOString());
        duration = Math.min(7, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));
      }
    }

    const ouraLogs = await syncOuraData(userDetails, duration);
    return res.status(200).json({
      success: true,
      data: ouraLogs,
    });
  } catch (error) {
    log.error(`Oura | ManualSync error, ${JSON.stringify(error)}`);
    return next({ message: error?.message, statusCode: 500 });
  }
};

// Hourly Sync with Oura
async function syncOuraData(userDetails, duration = 2) {
  try {
    const UTCOffsetMin = await getUTCOffsetValue(userDetails.userId);
    if(!userDetails?.offsetFromUTCMillis && userDetails?.offsetFromUTCMillis != 0)
      userDetails.offsetFromUTCMillis = UTCOffsetMin * 60 * 1000;

    log.info(`Syncing Oura data for userID: ${userDetails.userId}`)
    log.info(`lastSynced from DB: ${userDetails.lastSynced} | offsetFromUTCMillis: ${userDetails?.offsetFromUTCMillis} | duration: ${duration}`);
    let lastSynced;
    if (duration === 0) {
      lastSynced = new Date(userDetails.lastSynced);
      lastSynced.setMilliseconds(lastSynced.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    } else {
      lastSynced = new Date(Date.now() + userDetails?.offsetFromUTCMillis || 0);
      lastSynced.setDate(lastSynced.getDate() - duration);
    }
    log.info("offsetFromUTCMillis adjusted lastSynced: ", lastSynced.toISOString())
    const sources = await getStaticSources();

    // Fetch user's access token (after refresh)
    const accessToken = await getOuraAccessToken(userDetails.userId);
    log.debug("Retreived Access token");

    if (!accessToken) {
      throw new Error("Access token not found. Please ensure that you have a valid access token.");
    }

    // Fetch all activitySummary, activityList, sleep logs, hrvLogs & restingHeartRate
    const timeBasedTrackerLogs = await oura.tracker.getTimeBasedTrackersData(userDetails.userId, accessToken, lastSynced);
    log.info("Time based tracker logs");
    log.info(`timeBasedTrackerLogs: ${JSON.stringify(timeBasedTrackerLogs)}`);

    // Upsert day based trackers
    const currentDT = new Date();
    currentDT.setUTCHours(0,0,0,0)
    currentDT.setMilliseconds(currentDT.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    const lastSyncedDate = new Date(userDetails.lastSynced);
    lastSyncedDate.setUTCHours(0,0,0,0)
    lastSyncedDate.setMilliseconds(lastSyncedDate.getMilliseconds() + userDetails?.offsetFromUTCMillis || 0);
    if (duration) lastSyncedDate.setDate(lastSyncedDate.getDate() - duration);
    const allDayData = { heartRateLogs: [], spo2Logs: []};
    log.info(`currentDT: ${currentDT} | currentDT.toISOString(): ${currentDT.toISOString()}`)
    log.info("Last Sync Date", lastSyncedDate.toISOString())

    while (currentDT >= lastSyncedDate) {
      const startTime = currentDT.toISOString();
      const endTime = new Date(currentDT.getTime() + 24 * 60 * 60 * 1000 - 1).toISOString();

      log.info(`Calling day based trackers for startTime: ${startTime} & endTime: ${endTime}`)
      const dayBasedTrackersData = await oura.tracker.getDayBasedTrackersData(userDetails.userId, accessToken, startTime, endTime, userDetails?.offsetFromUTCMillis || 0);

      log.info("Inserting heartRateLogs log")
      if (dayBasedTrackersData?.heartRateLogs?.length > 0)
        await Promise.all(
          dayBasedTrackersData?.heartRateLogs.map(async (log) => {
            allDayData.heartRateLogs.push(log);
          })
        );

      log.info("Inserting SpO2 log")
      if (dayBasedTrackersData?.spo2Logs?.length > 0)
        await Promise.all(
          dayBasedTrackersData?.spo2Logs.map(async (log) => {
            allDayData.spo2Logs.push(log);
          })
        );

      currentDT.setDate(currentDT.getDate() - 1);
    }

    await postProcessSync(userDetails.userId, timeBasedTrackerLogs, allDayData);
    return { ...timeBasedTrackerLogs, ...allDayData };
  } catch (error) {
    log.error(`syncOuraData error for userId ${userDetails.userId}: ${error.message}`, error);
    throw error;
  }
}

async function postProcessSync(userId, timeBasedTrackerLogs, allDayData) {
  const trackerIdLogsMapping = {
    3: timeBasedTrackerLogs?.activitySummary || [],
    4: timeBasedTrackerLogs?.activityList || [],
    5: timeBasedTrackerLogs?.sleepLogs || [],
    13: timeBasedTrackerLogs?.ecgLogs || [],
    18: timeBasedTrackerLogs?.tempLogs || [],
    11: timeBasedTrackerLogs?.hrvLogs || [],
    25: timeBasedTrackerLogs?.restingHeartRateLogs || [],
    10: allDayData?.heartRateLogs || [],
    9: allDayData?.spo2Logs || [],
  }; 
  log.info(`oura postProcessSync for userId: ${userId}, trackerIdLogsMapping: ${JSON.stringify(trackerIdLogsMapping)}`);  
  const isPushedToSQS = await addLogsToQueue(userId, trackerIdLogsMapping);
  log.info(`oura postProcessSync for userId: ${userId}, isPushedToSQS: ${isPushedToSQS}`);
  return true;
}

async function updateDeviceScope(userId, ouraScope, accessToken) {
  const sources = await getStaticSources();
  const sourceDevices = await getStaticSourceDevices();
  const scope = ouraScope?.split(' ') || []
  const trackers = []
  scope.forEach(tr => {
    const trackerIds = trackersOuraScopeMap?.[tr] || [];
    trackerIds.forEach(tId => trackers.push({trackerId: Number(tId), isEnabled: true}))
  })

  const document = {
    userId,
    newDevice: {
      id: sourceDevices[sources.Oura.id]?.[0]?.id,
      trackers, isConnected: true
    },
    updatedAt: new Date().toISOString()
  };
  await devicesService.connectNewDevices(userId, document, true, accessToken);  
}

async function getOuraAccessToken(userId) {
  const sources = await getStaticSources();
  log.info(`Capturing oura access token for userId: ${userId}`)
  const userDetails = await userService.getUserDetails(userId, sources.Oura.id);
  if (userDetails && userDetails.accessToken) {
    const currentDate = new Date();
    const expiryDate = new Date(userDetails.expiresAt);
    if (expiryDate.getTime() >= currentDate.getTime()) {
      log.info("Returning current access token")
      return userDetails.accessToken;
    } else {
      log.info("Generating from refresh token")
      try {
        const ouraResponse = await oura.auth.getRefreshToken(
          userDetails.refreshToken
        );
        await storeOuraDetails(userId, ouraResponse);
        log.info(`Successfully generated token for userId: ${userId}`, ouraResponse.access_token)
        return ouraResponse.access_token;
      } catch (error) {
        const errorStatus = error.response?.status;
        if (errorStatus >= 400 && errorStatus < 500) {
            await revokeOuraToken(userId, false);
        }
        log.warn(`Error retreiving Oura access token for userId: ${userId}`)
        return null;
      }
    }
  }
  log.warn(`Failed to fetch user details for userId: ${userId}`)
  return null;
}

async function getOAuthUrl(userId, isUpdateLastSynced = true) {
  // Create State and Register User in OpenSearch
  const state = uuidv4();
  const sources = await getStaticSources();
  const date = new Date().toISOString();
  const deviceId = await devicesService.getDefaultDeviceIdBySource(sources.Oura.id);
  const userData = {
    state,
    source: sources.Oura.name,
    sourceId: sources.Oura.id,
    trackers: sources.Oura.trackers,
    createdAt: date,
    updatedAt: date
  };

  if (isUpdateLastSynced) {
    userData.devices = {
      [deviceId]: {
        lastSynced: date,
      },
    };
    userData.lastSynced = date;
  }

  await userService.upsertUserDetails(userId, userData, sources.Oura.id);

  // Not passing scope, as all the data is necessary, https://cloud.ouraring.com/docs/authentication
  const authorizeUrl = `${config.oura.authorize_url}?client_id=${config.oura.client_id}` +
    `&redirect_uri=${config.oura.authorize_redirect}&response_type=code&scope=email+personal+daily+heartrate+workout+tag+session+spo2&state=${state}&prompt=consent`;

  return authorizeUrl;
}

module.exports = {
  getMetadata,
  callback,
  ManualSync,
  syncOuraData,
  revokeOuraToken,
  getOuraAccessToken,
  getOAuthUrl,
}
