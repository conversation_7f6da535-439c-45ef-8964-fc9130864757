const { v4: uuidv4 } = require("uuid");
const { log } = require("../utils/logger");
const { getDateAfterSeconds } = require("../utils/helpers");
const { config } = require("../environment/index");
const dexcom = require("../sources/dexcom");
const { userService } = require("../service/users")
const { devicesService } = require("../service/devices");
const { trackersService } = require("../service/trackers");
const { addLogsToQueue } = require("../utils/trackersIngestion");
const { getStaticSources, getStaticSourceDevices } = require("../utils/staticData");

// Auth Redirect
async function getMetadata(req, res, next) {
  try {
    const userId = req.decoded.user_guid;
    if (!userId) {
      return res.status(400).send({ success: false, message: "userId is missing in request" });
    }

    const authorizeUrl = await getOAuthUrl(userId);
    return res.status(200).json({
      success: true,
      message: 'Visit Authorise URL to proceed with OAuth Login.',
      data: {
        authorizeUrl,
      },
    });
  } catch (error) {
    log.error(`Dexcom | getMetadata error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get metadata", statusCode: 500 });
  }
};

// Auth0 Callback
async function callback(req, res, next) {
  try {
    const code = req.query.code;
    const state = req.query.state;
    if (!code || !state) {
      return res.status(400).send({
        success: false,
        message: "Required query param code/state missing",
      });
    }
    const sources = await getStaticSources();
    const userDetails = await userService.getUserByState(state, sources.Dexcom.id);
    log.debug(`Dexcom callback userDetails`, JSON.stringify(userDetails));
    if (!userDetails) {
      return res.status(400).send({
        success: false,
        message: "Invalid state input provided",
      });
    }

    const dexcomResponse = await dexcom.auth.getAccessToken(code);
    log.debug(`getAccessToken for userId: ${userDetails.userId}`, JSON.stringify(dexcomResponse));

    await storeDexcomDetails(userDetails.userId, dexcomResponse);
    const accessToken = req.headers['x-access-token'];
    await updateDeviceScope(userDetails.userId, accessToken);

    // Syncing logs for duration = 7, once device is connected
    const dexcomLogs = await syncDexcomData(userDetails, 7);

    return res.status(200).json({
      success: true,
      message: "Success",
    });
  } catch (error) {
    log.error(`Dexcom | callback error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to process callback", statusCode: 500 });
  }
};

// Calibrations: Not in Use
async function calibrations(req, res, next) {
  try {
    let { startDate, endDate } = req.query;
    const { user_id: userId, user_guid: userGuid } = req.decoded;
    if (!userId || !userGuid) {
      return res.status(400).send({
        success: false,
        message: "user_id missing in request",
      });
    }

    if (!startDate || !endDate) {
      return next({
        message: "Start Date & End Date are mandatory params",
        statusCode: 400,
      });
    }

    startDate = new Date(startDate);
    endDate = new Date(endDate);

    if (startDate === "Invalid Date" || endDate === "Invalid Date") {
      return next({
        message: "Invalid date params",
        statusCode: 400,
      });
    }

    startDate = startDate.toISOString().split(".")[0];
    endDate = endDate.toISOString().split(".")[0];

    const accessToken = await getDexcomAccessToken(userGuid);
    log.debug(accessToken);

    const data = await dexcom.tracker.getCalibrations(
      accessToken,
      startDate,
      endDate
    );

    if (data) {
      return res.status(200).json({
        success: true,
        data,
      });
    }

    res.status(500).json({
      success: false,
      message: "Something went wrong",
    });
  } catch (error) {
    log.error(`Dexcom | calibrations error: ${JSON.stringify(error)}`);
    return next({ message: error?.message || "Failed to get calibrations", statusCode: 500 });
  }
};

// Revoke Token
async function revokeDexcomToken(userId) {
  try {
    // TODO
    // Revoke access token (not provided by Dexcom)
    log.info(`Revoking dexcom token for userId: ${userId}`);

    const sources = await getStaticSources();
    const sourceDevices = await getStaticSourceDevices();

    // Remove user Dexcom details
    const deletedUserDoc = await userService.deleteUser(userId, sources.Dexcom.id);
    if (!deletedUserDoc) {log.info("Failed to delete user")}

    // Remove Dexcom default device
    const resp = await trackersService.getAllDefaultTrackers(userId)
    resp.defaultDevices = resp.defaultDevices.filter(d => {
      d.deviceId != sourceDevices[sources.Dexcom.id]?.[0]?.id
    })
    await trackersService.upsertTrackers(userId, resp)

    // Remove connected device
    const deviceIds = Object.keys(deletedUserDoc?.devices || {});
    await devicesService.deleteConnectedDevice(userId, deviceIds);

    log.info(`Revoked user token for Dexcom for userId: ${userId}`)
  } catch (error) {
    log.error(`Dexcom | revokeDexcomToken error for userId ${userId}: ${JSON.stringify(error)}`);
    throw error; // Re-throw since this function is called by other controllers
  }
}

async function ManualSync(req, res, next) {
  const userId = req.query?.user_guid || req.decoded.user_guid;
  const duration = Number(req.query?.duration || 7);
  if (!userId)
    return res.status(400).send({ success: false, message: "user_id missing in request", });

  const sources = await getStaticSources();
  const userDetails = await userService.getUserDetails(userId, sources.Dexcom.id);
  if (!userDetails)
    return res.status(400).send({ success: false, message: "User is not connected with Dexcom", });

  try {
    const dexcomLogs = await syncDexcomData(userDetails, duration);
    return res.status(200).json({
      success: true,
      data: dexcomLogs,
    });
  }
  catch (error) {
    log.error(`Dexcom | ManualSync error, ${JSON.stringify(error)}`);
    return next({ message: error?.message, statusCode: 401 });
  }
};

// Hourly Sync with Dexcom
async function syncDexcomData(userDetails, duration = 2) {
  let lastSynced;
  if (duration == 0 && userDetails.lastSynced) {
    lastSynced = new Date(userDetails.lastSynced);
  } else {
    lastSynced = new Date();
    lastSynced.setDate(lastSynced.getDate() - duration);
  }

  log.info(`Syncing Dexcom data for userID: ${userDetails.userId}`)
  log.info(`lastSynced from DB: ${userDetails.lastSynced} | duration: ${duration}`);

  // Fetch user's access token (after refresh)
  const accessToken = await getDexcomAccessToken(userDetails.userId);
  log.info("accesstoken: ", accessToken)

  if (!accessToken) {
    throw new Error("Access token not found. Please ensure that you have a valid access token.");
  }
  
  // Get lastest EGVS date info
  const dexcomDateRange = await dexcom.tracker.getDataDateRange(accessToken);
  if (!dexcomDateRange) return
  let egvsEndDate = new Date(dexcomDateRange.egvs.end.systemTime)

  // If last datapoint is before lastSync, return
  // if (egvsEndDate.getTime() <= lastSynced.getTime()) return;

  lastSynced = lastSynced.toISOString().split(".")[0];
  egvsEndDate = egvsEndDate.toISOString().split(".")[0];
  log.info("Start Date", lastSynced);
  log.info("End Date", egvsEndDate);

  // Fetch EGVS logs
  const egvsLogs = await dexcom.tracker.getEGVS(accessToken, lastSynced, egvsEndDate, userDetails.userId);
  log.info(`egvsLogs: ${JSON.stringify(egvsLogs)}`);
  await postProcessSync(userDetails.userId, egvsLogs);
  return egvsLogs;
}

async function postProcessSync(userId, egvsLogs) {
  const trackerIdLogsMapping = {
    8: egvsLogs || [],
  };
  log.info(`dexcom postProcessSync for userId: ${userId}, trackerIdLogsMapping: ${JSON.stringify(trackerIdLogsMapping)}`);
  const isPushedToSQS = await addLogsToQueue(userId, trackerIdLogsMapping);
  log.info(`dexcom postProcessSync for userId: ${userId}, isPushedToSQS: ${isPushedToSQS}`);
  return egvsLogs;
}

async function storeDexcomDetails(userId, dexcomResponse) {
  const sources = await getStaticSources();
  const data = {
    accessToken: dexcomResponse?.access_token,
    refreshToken: dexcomResponse?.refresh_token,
  };
  data.expiresAt =
    data.accessToken && dexcomResponse.expires_in
      ? getDateAfterSeconds(dexcomResponse.expires_in - 60)
      : null;
  await userService.upsertUserDetails(userId, data, sources.Dexcom.id);
}

async function updateDeviceScope(userId, accessToken) {
  const sources = await getStaticSources();
  const sourceDevices = await getStaticSourceDevices();
  const trackers = [{trackerId: 8, isEnabled: true}]
  const document = {
    userId,
    newDevice: {
      id: sourceDevices[sources.Dexcom.id]?.[0]?.id,
      trackers, isConnected: true
    },
    updatedAt: new Date().toISOString()
  };
  await devicesService.connectNewDevices(userId, document, true, accessToken);  
}

async function getDexcomAccessToken(userId) {
  const sources = await getStaticSources();
  log.info(`Capturing dexcom access token for userId: ${userId}`)
  const userDetails = await userService.getUserDetails(userId, sources.Dexcom.id);
  if (userDetails && userDetails.accessToken) {
    const currentDate = new Date();
    const expiryDate = new Date(userDetails.expiresAt);
    if (expiryDate.getTime() >= currentDate.getTime()) {
      log.info("Returning current access token")
      return userDetails.accessToken;
    } else {
      log.info("Generating from refresh token")
      try {
        const dexcomResponse = await dexcom.auth.getRefreshToken(
          userDetails.refreshToken
        );
        await storeDexcomDetails(userId, dexcomResponse);
        log.info(`Successfully generated token for userId: ${userId}`, dexcomResponse.access_token)
        return dexcomResponse.access_token;
      } catch (error) {
        log.warn(`Error retreiving Dexcom access token for userId; ${userId}`, JSON.stringify(error))
        return null;
      }
    }
  }
  log.warn(`Failed to fetch user details for userId: ${userId}`)
  return null;
}

async function getOAuthUrl(userId, isUpdateLastSynced = true) {
  // Create State and Register User in OpenSearch
  const state = uuidv4();
  const sources = await getStaticSources();
  const date = new Date().toISOString();
  const deviceId = await devicesService.getDefaultDeviceIdBySource(sources.Dexcom.id);
  const userData = {
    state,
    source: sources.Dexcom.name,
    sourceId: sources.Dexcom.id,
    trackers: sources.Dexcom.trackers,
    createdAt: date,
    updatedAt: date
  };

  if (isUpdateLastSynced) {
    userData.devices = {
      [deviceId]: {
        lastSynced: date,
      },
    };
    userData.lastSynced = date;
  }

  await userService.upsertUserDetails(userId, userData, sources.Dexcom.id);

  const authorizeUrl = `${config.dexcom.authorize_url}?client_id=${config.dexcom.client_id}` +
    `&redirect_uri=${config.dexcom.authorize_redirect}&response_type=code&scope=offline_access&state=${state}&force_login=true`;
  
  return authorizeUrl;
}

module.exports = {
  getMetadata, 
  callback,
  calibrations, 
  revokeDexcomToken,
  ManualSync,
  syncDexcomData,
  getOAuthUrl,
}
