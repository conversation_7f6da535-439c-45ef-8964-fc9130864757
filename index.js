const express = require("express");
const bodyParser = require("body-parser");
const serverless = require('serverless-http');
const nconf = require("nconf");
const routes = require("./controllers/index");
const { log } = require("./utils/logger");
const { config } = require("./environment/index");
const { createClient } = require("./utils/connection");
const { getStaticData } = require("./utils/staticData");
const app = express();

app.use(express.json({ limit: '2mb' }));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true, limit: '2mb' }));

// Request Logging Middleware
app.all("/*", (req, res, next) => {
  const obj = {
    method: req.method,
    url: req.originalUrl,
    headers: req.headers,
  };
  if (req.query) {
    obj.query = req.query;
  }
  if (req.body) {
    obj.body = req.body;
  }
  const url = obj?.url || "path not found";
  log.info(obj, "API request received, Path: " + url);

  next();
});

app.use("/trackers", routes.router);
app.use("/trackers/api/v1", routes.authenticatedRouter);

// Custom Error handler middleware
app.use((err, req, res, next) => {
  log.warn("Error occurred in API handler", { url: req.originalUrl, err });

  const statusCode = err.statusCode || 500;
  const message = err.statusCode
    ? err.message
    : "Internal Error: Something went wrong";
  res.status(statusCode).json({
    success: false,
    message,
    ...(err.error ? {error: err.error} : {}),
  });
});

const env = nconf.get('NODE_ENV') || 'development'

if (env == 'development') {
  const server = app.listen(config.PORT, async () => {
    try {

      process.env['AWS_ACCESS_KEY_ID'] = "********************";
      process.env['AWS_SECRET_ACCESS_KEY'] = "BtH1XFJC4MNa2ntsTSpWo7sx1hKl/iW9ghqaIrm2";
      
      await getStaticData();
      await createClient();
      log.info(`🚀 Listening on port ${config.PORT}`);
    } catch (error) {
      log.fatal(error);
      server.close();
    }
  });
} else {
  // For Lambda environments, environment variables are already set in Lambda configuration
  getStaticData();
  module.exports.handler = serverless(app);
  createClient();
}
